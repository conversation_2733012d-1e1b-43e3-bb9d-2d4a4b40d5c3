//@version=6
indicator("Session Liquidity Levels - Trading Day Aware", "SLL-TDA", overlay=true, max_lines_count=50, max_labels_count=50)

// ═══════════════════════════════════════════════════════════════════════════
//                          TRADING DAY LOGIC EXPLANATION
// ═══════════════════════════════════════════════════════════════════════════
// 
// PROBLEM: Asian sessions (7 PM - 12 AM) span calendar dates, causing display
// inconsistencies between daily and intraday charts due to timezone handling.
//
// SOLUTION: Implement "Trading Day" calculation where:
// - Trading day starts at 6 PM NY time (18:00)
// - If current time >= 18:00 NY, it belongs to NEXT calendar day's trading session
// - This ensures consistent session grouping across all timeframes
// - 3-day window = Today, Yesterday, Day before yesterday (based on trading days)
//
// EXAMPLE:
// - Thursday 7 PM NY = Friday trading day (Asia session)
// - Thursday 11 PM NY = Friday trading day (still Asia session)  
// - Friday 1 AM NY = Friday trading day (London session)
// - Friday 9:30 AM NY = Friday trading day (NY AM session)
//
// ═══════════════════════════════════════════════════════════════════════════

// ═══════════════════════════════════════════════════════════════════════════
//                               INPUTS
// ═══════════════════════════════════════════════════════════════════════════

// Session toggles
showAsia = input.bool(true, "Show Asia Session", group="Sessions")
showLondon = input.bool(true, "Show London Session", group="Sessions")
showPreMarket = input.bool(true, "Show Pre-Market Session", group="Sessions")
showNYAM = input.bool(true, "Show NY AM Session", group="Sessions")
showLunch = input.bool(true, "Show Lunch Session", group="Sessions")
showNYPM = input.bool(true, "Show NY PM Session", group="Sessions")

// Level toggles
showHighs = input.bool(true, "Show Session Highs", group="Levels")
showLows = input.bool(true, "Show Session Lows", group="Levels")
showMitigated = input.bool(true, "Show Mitigated Levels", group="Levels")

// Visual settings
lineExtension = input.int(25, "Line Extension (bars)", minval=5, maxval=100, group="Visual")
showLabels = input.bool(true, "Show Labels", group="Visual")

// Colors
asiaColor = input.color(color.purple, "Asia Color", group="Colors")
londonColor = input.color(color.blue, "London Color", group="Colors")
preMarketColor = input.color(color.yellow, "Pre-Market Color", group="Colors")
nyamColor = input.color(color.green, "NY AM Color", group="Colors")
lunchColor = input.color(color.gray, "Lunch Color", group="Colors")
nypmColor = input.color(color.orange, "NY PM Color", group="Colors")
mitigatedColor = input.color(color.red, "Mitigated Color", group="Colors")

// ═══════════════════════════════════════════════════════════════════════════
//                         TRADING DAY CALCULATION
// ═══════════════════════════════════════════════════════════════════════════

// Trading day starts at 6 PM NY time (18:00)
// If current time >= 18:00 NY, it belongs to the NEXT calendar day's trading session
getTradingDay(barTime) =>
    nyHour = hour(barTime, "America/New_York")
    nyYear = year(barTime, "America/New_York")
    nyMonth = month(barTime, "America/New_York")
    nyDay = dayofmonth(barTime, "America/New_York")
    
    // If hour >= 18 (6 PM), this bar belongs to next day's trading session
    if nyHour >= 18
        // Add one day to get the trading day
        nextDayTimestamp = timestamp("America/New_York", nyYear, nyMonth, nyDay + 1, 0, 0)
        dayofmonth(nextDayTimestamp, "America/New_York")
    else
        nyDay

// Get current trading day for filtering
getCurrentTradingDay() =>
    getTradingDay(timenow)

// Check if a trading day is within our 3-day window
isWithin3TradingDays(tradingDay) =>
    currentDay = getCurrentTradingDay()
    dayDiff = math.abs(currentDay - tradingDay)
    dayDiff <= 2

// ═══════════════════════════════════════════════════════════════════════════
//                            CORE FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════

// Simple session detection
inSession(sessionStr) =>
    not na(time(timeframe.period, sessionStr, "America/New_York"))

newSession(sessionStr) =>
    t = time(timeframe.period, sessionStr, "America/New_York")
    na(t[1]) and not na(t)

// Enhanced session detection with trading day awareness
newSessionWithTradingDay(sessionStr) =>
    isNewSession = newSession(sessionStr)
    currentTradingDay = getTradingDay(time)
    [isNewSession, currentTradingDay]

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DEFINITIONS
// ═══════════════════════════════════════════════════════════════════════════

asiaSessionStr = "1900-0000:1234567"
londonSessionStr = "0200-0500:23456"
preMarketSessionStr = "0700-0930:23456"
nyamSessionStr = "0930-1200:23456"
lunchSessionStr = "1200-1300:23456"
nypmSessionStr = "1330-1610:23456"

// ═══════════════════════════════════════════════════════════════════════════
//                        SESSION HIGH/LOW TRACKING
// ═══════════════════════════════════════════════════════════════════════════

// Asia session variables
var float asiaHigh = na
var float asiaLow = na
var int asiaHighBar = na
var int asiaLowBar = na
var bool asiaHighMitigated = false
var bool asiaLowMitigated = false
var int asiaTradingDay = na

// London session variables  
var float londonHigh = na
var float londonLow = na
var int londonHighBar = na
var int londonLowBar = na
var bool londonHighMitigated = false
var bool londonLowMitigated = false
var int londonTradingDay = na

// Pre-Market session variables
var float preMarketHigh = na
var float preMarketLow = na
var int preMarketHighBar = na
var int preMarketLowBar = na
var bool preMarketHighMitigated = false
var bool preMarketLowMitigated = false
var int preMarketTradingDay = na

// NY AM session variables
var float nyamHigh = na
var float nyamLow = na
var int nyamHighBar = na
var int nyamLowBar = na
var bool nyamHighMitigated = false
var bool nyamLowMitigated = false
var int nyamTradingDay = na

// Lunch session variables
var float lunchHigh = na
var float lunchLow = na
var int lunchHighBar = na
var int lunchLowBar = na
var bool lunchHighMitigated = false
var bool lunchLowMitigated = false
var int lunchTradingDay = na

// NY PM session variables
var float nypmHigh = na
var float nypmLow = na
var int nypmHighBar = na
var int nypmLowBar = na
var bool nypmHighMitigated = false
var bool nypmLowMitigated = false
var int nypmTradingDay = na

// ═══════════════════════════════════════════════════════════════════════════
//                           SESSION PROCESSING
// ═══════════════════════════════════════════════════════════════════════════

// Asia Session
[asiaNewSession, asiaSessionTradingDay] = newSessionWithTradingDay(asiaSessionStr)
if asiaNewSession
    asiaHigh := high
    asiaLow := low
    asiaHighBar := bar_index
    asiaLowBar := bar_index
    asiaHighMitigated := false
    asiaLowMitigated := false
    asiaTradingDay := asiaSessionTradingDay

if inSession(asiaSessionStr) and isWithin3TradingDays(asiaTradingDay)
    if high > asiaHigh
        asiaHigh := high
        asiaHighBar := bar_index
        asiaHighMitigated := false
    if low < asiaLow
        asiaLow := low
        asiaLowBar := bar_index
        asiaLowMitigated := false

// London Session
[londonNewSession, londonSessionTradingDay] = newSessionWithTradingDay(londonSessionStr)
if londonNewSession
    londonHigh := high
    londonLow := low
    londonHighBar := bar_index
    londonLowBar := bar_index
    londonHighMitigated := false
    londonLowMitigated := false
    londonTradingDay := londonSessionTradingDay

if inSession(londonSessionStr) and isWithin3TradingDays(londonTradingDay)
    if high > londonHigh
        londonHigh := high
        londonHighBar := bar_index
        londonHighMitigated := false
    if low < londonLow
        londonLow := low
        londonLowBar := bar_index
        londonLowMitigated := false

// Pre-Market Session
[preMarketNewSession, preMarketSessionTradingDay] = newSessionWithTradingDay(preMarketSessionStr)
if preMarketNewSession
    preMarketHigh := high
    preMarketLow := low
    preMarketHighBar := bar_index
    preMarketLowBar := bar_index
    preMarketHighMitigated := false
    preMarketLowMitigated := false
    preMarketTradingDay := preMarketSessionTradingDay

if inSession(preMarketSessionStr) and isWithin3TradingDays(preMarketTradingDay)
    if high > preMarketHigh
        preMarketHigh := high
        preMarketHighBar := bar_index
        preMarketHighMitigated := false
    if low < preMarketLow
        preMarketLow := low
        preMarketLowBar := bar_index
        preMarketLowMitigated := false

// NY AM Session
[nyamNewSession, nyamSessionTradingDay] = newSessionWithTradingDay(nyamSessionStr)
if nyamNewSession
    nyamHigh := high
    nyamLow := low
    nyamHighBar := bar_index
    nyamLowBar := bar_index
    nyamHighMitigated := false
    nyamLowMitigated := false
    nyamTradingDay := nyamSessionTradingDay

if inSession(nyamSessionStr) and isWithin3TradingDays(nyamTradingDay)
    if high > nyamHigh
        nyamHigh := high
        nyamHighBar := bar_index
        nyamHighMitigated := false
    if low < nyamLow
        nyamLow := low
        nyamLowBar := bar_index
        nyamLowMitigated := false

// Lunch Session
[lunchNewSession, lunchSessionTradingDay] = newSessionWithTradingDay(lunchSessionStr)
if lunchNewSession
    lunchHigh := high
    lunchLow := low
    lunchHighBar := bar_index
    lunchLowBar := bar_index
    lunchHighMitigated := false
    lunchLowMitigated := false
    lunchTradingDay := lunchSessionTradingDay

if inSession(lunchSessionStr) and isWithin3TradingDays(lunchTradingDay)
    if high > lunchHigh
        lunchHigh := high
        lunchHighBar := bar_index
        lunchHighMitigated := false
    if low < lunchLow
        lunchLow := low
        lunchLowBar := bar_index
        lunchLowMitigated := false

// NY PM Session
[nypmNewSession, nypmSessionTradingDay] = newSessionWithTradingDay(nypmSessionStr)
if nypmNewSession
    nypmHigh := high
    nypmLow := low
    nypmHighBar := bar_index
    nypmLowBar := bar_index
    nypmHighMitigated := false
    nypmLowMitigated := false
    nypmTradingDay := nypmSessionTradingDay

if inSession(nypmSessionStr) and isWithin3TradingDays(nypmTradingDay)
    if high > nypmHigh
        nypmHigh := high
        nypmHighBar := bar_index
        nypmHighMitigated := false
    if low < nypmLow
        nypmLow := low
        nypmLowBar := bar_index
        nypmLowMitigated := false

// ═══════════════════════════════════════════════════════════════════════════
//                          MITIGATION DETECTION
// ═══════════════════════════════════════════════════════════════════════════

// Check mitigation for all levels (only for levels within 3-day window)
if not asiaHighMitigated and not na(asiaHigh) and isWithin3TradingDays(asiaTradingDay) and high > asiaHigh
    asiaHighMitigated := true

if not asiaLowMitigated and not na(asiaLow) and isWithin3TradingDays(asiaTradingDay) and low < asiaLow
    asiaLowMitigated := true

if not londonHighMitigated and not na(londonHigh) and isWithin3TradingDays(londonTradingDay) and high > londonHigh
    londonHighMitigated := true

if not londonLowMitigated and not na(londonLow) and isWithin3TradingDays(londonTradingDay) and low < londonLow
    londonLowMitigated := true

if not preMarketHighMitigated and not na(preMarketHigh) and isWithin3TradingDays(preMarketTradingDay) and high > preMarketHigh
    preMarketHighMitigated := true

if not preMarketLowMitigated and not na(preMarketLow) and isWithin3TradingDays(preMarketTradingDay) and low < preMarketLow
    preMarketLowMitigated := true

if not nyamHighMitigated and not na(nyamHigh) and isWithin3TradingDays(nyamTradingDay) and high > nyamHigh
    nyamHighMitigated := true

if not nyamLowMitigated and not na(nyamLow) and isWithin3TradingDays(nyamTradingDay) and low < nyamLow
    nyamLowMitigated := true

if not lunchHighMitigated and not na(lunchHigh) and isWithin3TradingDays(lunchTradingDay) and high > lunchHigh
    lunchHighMitigated := true

if not lunchLowMitigated and not na(lunchLow) and isWithin3TradingDays(lunchTradingDay) and low < lunchLow
    lunchLowMitigated := true

if not nypmHighMitigated and not na(nypmHigh) and isWithin3TradingDays(nypmTradingDay) and high > nypmHigh
    nypmHighMitigated := true

if not nypmLowMitigated and not na(nypmLow) and isWithin3TradingDays(nypmTradingDay) and low < nypmLow
    nypmLowMitigated := true

// ═══════════════════════════════════════════════════════════════════════════
//                            VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════

// Helper function to draw level with trading day filtering
drawLevel(levelPrice, levelBar, levelColor, sessionName, isHigh, isMitigated, showSession, showType, tradingDay) =>
    if showSession and showType and not na(levelPrice) and isWithin3TradingDays(tradingDay)
        // Determine line style and color
        actualColor = isMitigated and showMitigated ? mitigatedColor : levelColor
        actualStyle = isMitigated ? line.style_dashed : line.style_solid
        actualWidth = isMitigated ? 1 : 2
        
        // Only draw if conditions are met
        shouldDraw = isMitigated ? showMitigated : true
        
        if shouldDraw
            // Draw line
            rightBar = bar_index + lineExtension
            line.new(levelBar, levelPrice, rightBar, levelPrice, color=actualColor, width=actualWidth, style=actualStyle, extend=extend.right)
            
            // Draw label if enabled
            if showLabels
                labelText = sessionName + (isHigh ? " High" : " Low")
                if isMitigated
                    labelText := labelText + " ✓"
                
                // Add trading day info to label for debugging
                labelText := labelText + "\n" + str.tostring(levelPrice, format.mintick)
                labelText := labelText + "\nTD:" + str.tostring(tradingDay)
                
                labelStyle = isHigh ? label.style_label_down : label.style_label_up
                label.new(levelBar, levelPrice, labelText, style=labelStyle, color=actualColor, textcolor=color.white, size=size.small)

// Draw Asia levels
drawLevel(asiaHigh, asiaHighBar, asiaColor, "Asia", true, asiaHighMitigated, showAsia, showHighs, asiaTradingDay)
drawLevel(asiaLow, asiaLowBar, asiaColor, "Asia", false, asiaLowMitigated, showAsia, showLows, asiaTradingDay)

// Draw London levels
drawLevel(londonHigh, londonHighBar, londonColor, "London", true, londonHighMitigated, showLondon, showHighs, londonTradingDay)
drawLevel(londonLow, londonLowBar, londonColor, "London", false, londonLowMitigated, showLondon, showLows, londonTradingDay)

// Draw Pre-Market levels
drawLevel(preMarketHigh, preMarketHighBar, preMarketColor, "PreMkt", true, preMarketHighMitigated, showPreMarket, showHighs, preMarketTradingDay)
drawLevel(preMarketLow, preMarketLowBar, preMarketColor, "PreMkt", false, preMarketLowMitigated, showPreMarket, showLows, preMarketTradingDay)

// Draw NY AM levels
drawLevel(nyamHigh, nyamHighBar, nyamColor, "NY AM", true, nyamHighMitigated, showNYAM, showHighs, nyamTradingDay)
drawLevel(nyamLow, nyamLowBar, nyamColor, "NY AM", false, nyamLowMitigated, showNYAM, showLows, nyamTradingDay)

// Draw Lunch levels
drawLevel(lunchHigh, lunchHighBar, lunchColor, "Lunch", true, lunchHighMitigated, showLunch, showHighs, lunchTradingDay)
drawLevel(lunchLow, lunchLowBar, lunchColor, "Lunch", false, lunchLowMitigated, showLunch, showLows, lunchTradingDay)

// Draw NY PM levels  
drawLevel(nypmHigh, nypmHighBar, nypmColor, "NY PM", true, nypmHighMitigated, showNYPM, showHighs, nypmTradingDay)
drawLevel(nypmLow, nypmLowBar, nypmColor, "NY PM", false, nypmLowMitigated, showNYPM, showLows, nypmTradingDay)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION BACKGROUNDS
// ═══════════════════════════════════════════════════════════════════════════

// Optional session backgrounds
bgcolor(inSession(asiaSessionStr) and showAsia ? color.new(asiaColor, 95) : na, title="Asia Session")
bgcolor(inSession(londonSessionStr) and showLondon ? color.new(londonColor, 95) : na, title="London Session")
bgcolor(inSession(preMarketSessionStr) and showPreMarket ? color.new(preMarketColor, 95) : na, title="Pre-Market Session")
bgcolor(inSession(nyamSessionStr) and showNYAM ? color.new(nyamColor, 95) : na, title="NY AM Session")
bgcolor(inSession(lunchSessionStr) and showLunch ? color.new(lunchColor, 95) : na, title="Lunch Session")
bgcolor(inSession(nypmSessionStr) and showNYPM ? color.new(nypmColor, 95) : na, title="NY PM Session")

// ═══════════════════════════════════════════════════════════════════════════
//                            STATUS TABLE
// ═══════════════════════════════════════════════════════════════════════════

var table statusTable = table.new(position.top_right, 4, 8, bgcolor=color.new(color.black, 80))

if barstate.islast
    currentTD = getCurrentTradingDay()
    currentNYHour = hour(timenow, "America/New_York")
    
    // Header
    table.cell(statusTable, 0, 0, "Session", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 1, 0, "High", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 2, 0, "Low", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 3, 0, "TD", text_color=color.white, bgcolor=color.new(color.gray, 50))
    
    // Current Trading Day Info
    table.cell(statusTable, 0, 1, "Current TD", text_color=color.white, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 1, 1, str.tostring(currentTD), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 2, 1, "HR:" + str.tostring(currentNYHour), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 3, 1, "", text_color=color.white, bgcolor=color.new(color.blue, 50))
    
    // Asia
    table.cell(statusTable, 0, 2, "Asia", text_color=color.white)
    table.cell(statusTable, 1, 2, asiaHighMitigated ? "✓" : "○", text_color=asiaHighMitigated ? mitigatedColor : asiaColor)
    table.cell(statusTable, 2, 2, asiaLowMitigated ? "✓" : "○", text_color=asiaLowMitigated ? mitigatedColor : asiaColor)
    table.cell(statusTable, 3, 2, str.tostring(asiaTradingDay), text_color=isWithin3TradingDays(asiaTradingDay) ? color.green : color.red)
    
    // London
    table.cell(statusTable, 0, 3, "London", text_color=color.white)
    table.cell(statusTable, 1, 3, londonHighMitigated ? "✓" : "○", text_color=londonHighMitigated ? mitigatedColor : londonColor)
    table.cell(statusTable, 2, 3, londonLowMitigated ? "✓" : "○", text_color=londonLowMitigated ? mitigatedColor : londonColor)
    table.cell(statusTable, 3, 3, str.tostring(londonTradingDay), text_color=isWithin3TradingDays(londonTradingDay) ? color.green : color.red)
    
    // Pre-Market
    table.cell(statusTable, 0, 4, "PreMkt", text_color=color.white)
    table.cell(statusTable, 1, 4, preMarketHighMitigated ? "✓" : "○", text_color=preMarketHighMitigated ? mitigatedColor : preMarketColor)
    table.cell(statusTable, 2, 4, preMarketLowMitigated ? "✓" : "○", text_color=preMarketLowMitigated ? mitigatedColor : preMarketColor)
    table.cell(statusTable, 3, 4, str.tostring(preMarketTradingDay), text_color=isWithin3TradingDays(preMarketTradingDay) ? color.green : color.red)
    
    // NY AM
    table.cell(statusTable, 0, 5, "NY AM", text_color=color.white)
    table.cell(statusTable, 1, 5, nyamHighMitigated ? "✓" : "○", text_color=nyamHighMitigated ? mitigatedColor : nyamColor)
    table.cell(statusTable, 2, 5, nyamLowMitigated ? "✓" : "○", text_color=nyamLowMitigated ? mitigatedColor : nyamColor)
    table.cell(statusTable, 3, 5, str.tostring(nyamTradingDay), text_color=isWithin3TradingDays(nyamTradingDay) ? color.green : color.red)
    
    // Lunch
    table.cell(statusTable, 0, 6, "Lunch", text_color=color.white)
    table.cell(statusTable, 1, 6, lunchHighMitigated ? "✓" : "○", text_color=lunchHighMitigated ? mitigatedColor : lunchColor)
    table.cell(statusTable, 2, 6, lunchLowMitigated ? "✓" : "○", text_color=lunchLowMitigated ? mitigatedColor : lunchColor)
    table.cell(statusTable, 3, 6, str.tostring(lunchTradingDay), text_color=isWithin3TradingDays(lunchTradingDay) ? color.green : color.red)
    
    // NY PM
    table.cell(statusTable, 0, 7, "NY PM", text_color=color.white)
    table.cell(statusTable, 1, 7, nypmHighMitigated ? "✓" : "○", text_color=nypmHighMitigated ? mitigatedColor : nypmColor)
    table.cell(statusTable, 2, 7, nypmLowMitigated ? "✓" : "○", text_color=nypmLowMitigated ? mitigatedColor : nypmColor)
    table.cell(statusTable, 3, 7, str.tostring(nypmTradingDay), text_color=isWithin3TradingDays(nypmTradingDay) ? color.green : color.red)