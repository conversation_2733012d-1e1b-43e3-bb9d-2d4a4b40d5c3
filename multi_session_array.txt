//@version=6
indicator("3-Day Session Liquidity Array", overlay=true, max_lines_count=150, max_labels_count=150, max_boxes_count=50)

// ═══════════════════════════════════════════════════════════════════════════
//                               INPUTS
// ═══════════════════════════════════════════════════════════════════════════

showAsia = input.bool(true, "Show Asia", group="Sessions")
showLondon = input.bool(true, "Show London", group="Sessions")
showPreMarket = input.bool(true, "Show Pre-Market", group="Sessions")
showNYAM = input.bool(true, "Show NY AM", group="Sessions")
showLunch = input.bool(true, "Show Lunch", group="Sessions")
showNYPM = input.bool(true, "Show NY PM", group="Sessions")

showLabels = input.bool(true, "Show Labels", group="Visual")
daysToShow = input.int(3, "Days to Show", minval=1, maxval=5, group="Visual")

// Colors
asiaColor = input.color(color.purple, "Asia", group="Colors")
londonColor = input.color(color.blue, "London", group="Colors")
preMarketColor = input.color(color.yellow, "Pre-Market", group="Colors")
nyamColor = input.color(color.green, "NY AM", group="Colors")
lunchColor = input.color(color.gray, "Lunch", group="Colors")
nypmColor = input.color(color.orange, "NY PM", group="Colors")

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION STORAGE
// ═══════════════════════════════════════════════════════════════════════════

// Arrays to store multiple days of each session
var array<float> asiaHighs = array.new<float>()
var array<float> asiaLows = array.new<float>()
var array<int> asiaBars = array.new<int>()
var array<int> asiaDays = array.new<int>()

var array<float> londonHighs = array.new<float>()
var array<float> londonLows = array.new<float>()
var array<int> londonBars = array.new<int>()
var array<int> londonDays = array.new<int>()

var array<float> preMarketHighs = array.new<float>()
var array<float> preMarketLows = array.new<float>()
var array<int> preMarketBars = array.new<int>()
var array<int> preMarketDays = array.new<int>()

var array<float> nyamHighs = array.new<float>()
var array<float> nyamLows = array.new<float>()
var array<int> nyamBars = array.new<int>()
var array<int> nyamDays = array.new<int>()

var array<float> lunchHighs = array.new<float>()
var array<float> lunchLows = array.new<float>()
var array<int> lunchBars = array.new<int>()
var array<int> lunchDays = array.new<int>()

var array<float> nypmHighs = array.new<float>()
var array<float> nypmLows = array.new<float>()
var array<int> nypmBars = array.new<int>()
var array<int> nypmDays = array.new<int>()

// Current session tracking
var float currentSessionHigh = na
var float currentSessionLow = na
var int currentSessionBar = na
var string currentSessionType = ""

// ═══════════════════════════════════════════════════════════════════════════
//                          CORE FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════

inSession(sessionStr) =>
    not na(time(timeframe.period, sessionStr, "America/New_York"))

newSession(sessionStr) =>
    t = time(timeframe.period, sessionStr, "America/New_York")
    na(t[1]) and not na(t)

getCurrentTradingDay() =>
    dayofmonth(time_tradingday, "America/New_York")

// Clean old sessions beyond our window
cleanOldSessions(highs, lows, bars, days, maxDays) =>
    currentDay = getCurrentTradingDay()
    while array.size(days) > 0
        oldestDay = array.get(days, array.size(days) - 1)
        dayDiff = currentDay - oldestDay
        if dayDiff < -20
            dayDiff := dayDiff + 31
        else if dayDiff > 20
            dayDiff := dayDiff - 31
        
        if dayDiff > maxDays
            array.pop(highs)
            array.pop(lows)
            array.pop(bars)
            array.pop(days)
        else
            break
    true

// Store new session
storeSession(highs, lows, bars, days, sessionHigh, sessionLow, sessionBar, sessionDay) =>
    array.unshift(highs, sessionHigh)
    array.unshift(lows, sessionLow)
    array.unshift(bars, sessionBar)
    array.unshift(days, sessionDay)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DETECTION
// ═══════════════════════════════════════════════════════════════════════════

// Sessions
asiaSession = "1900-0000:23456"
londonSession = "0200-0500:23456"
preMarketSession = "0700-0930:23456"
nyamSession = "0930-1200:23456"
lunchSession = "1200-1300:23456"
nypmSession = "1330-1610:23456"

// Track current session
if newSession(asiaSession)
    if currentSessionType != ""
        // Store previous session
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        if currentSessionType == "Asia"
            storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "London"
            storeSession(londonHighs, londonLows, londonBars, londonDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "PreMarket"
            storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYAM"
            storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "Lunch"
            storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYPM"
            storeSession(nypmHighs, nypmLows, nypmBars, nypmDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "Asia"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(londonSession)
    if currentSessionType == "Asia"
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "London"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(preMarketSession)
    if currentSessionType == "London"
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(londonHighs, londonLows, londonBars, londonDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "PreMarket"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(nyamSession)
    if currentSessionType == "PreMarket"
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "NYAM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(lunchSession)
    if currentSessionType == "NYAM"
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "Lunch"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(nypmSession)
    if currentSessionType == "Lunch"
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    
    currentSessionType := "NYPM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

// Update current session highs/lows
if currentSessionType != ""
    if high > currentSessionHigh
        currentSessionHigh := high
        currentSessionBar := bar_index
    if low < currentSessionLow
        currentSessionLow := low

// Clean old sessions
if barstate.isconfirmed
    cleanOldSessions(asiaHighs, asiaLows, asiaBars, asiaDays, daysToShow)
    cleanOldSessions(londonHighs, londonLows, londonBars, londonDays, daysToShow)
    cleanOldSessions(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, daysToShow)
    cleanOldSessions(nyamHighs, nyamLows, nyamBars, nyamDays, daysToShow)
    cleanOldSessions(lunchHighs, lunchLows, lunchBars, lunchDays, daysToShow)
    cleanOldSessions(nypmHighs, nypmLows, nypmBars, nypmDays, daysToShow)

// ═══════════════════════════════════════════════════════════════════════════
//                          VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════

// Draw sessions from arrays
drawSessions(highs, lows, bars, days, sessionColor, sessionName, showSession) =>
    if showSession and array.size(highs) > 0
        for i = 0 to math.min(array.size(highs) - 1, daysToShow - 1)
            sessionHigh = array.get(highs, i)
            sessionLow = array.get(lows, i)
            sessionBar = array.get(bars, i)
            sessionDay = array.get(days, i)
            
            // Draw high
            line.new(sessionBar, sessionHigh, bar_index, sessionHigh, color=sessionColor, width=2, extend=extend.right)
            
            // Draw low
            line.new(sessionBar, sessionLow, bar_index, sessionLow, color=sessionColor, width=2, extend=extend.right)
            
            // Labels
            if showLabels
                label.new(sessionBar, sessionHigh, sessionName + " H\nD" + str.tostring(sessionDay), style=label.style_label_down, color=sessionColor, textcolor=color.white, size=size.small)
                label.new(sessionBar, sessionLow, sessionName + " L\nD" + str.tostring(sessionDay), style=label.style_label_up, color=sessionColor, textcolor=color.white, size=size.small)

// Draw all sessions
drawSessions(asiaHighs, asiaLows, asiaBars, asiaDays, asiaColor, "Asia", showAsia)
drawSessions(londonHighs, londonLows, londonBars, londonDays, londonColor, "London", showLondon)
drawSessions(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketColor, "PreMkt", showPreMarket)
drawSessions(nyamHighs, nyamLows, nyamBars, nyamDays, nyamColor, "NYAM", showNYAM)
drawSessions(lunchHighs, lunchLows, lunchBars, lunchDays, lunchColor, "Lunch", showLunch)
drawSessions(nypmHighs, nypmLows, nypmBars, nypmDays, nypmColor, "NYPM", showNYPM)

// Session backgrounds
bgcolor(inSession(asiaSession) and showAsia ? color.new(asiaColor, 95) : na)
bgcolor(inSession(londonSession) and showLondon ? color.new(londonColor, 95) : na)
bgcolor(inSession(preMarketSession) and showPreMarket ? color.new(preMarketColor, 95) : na)
bgcolor(inSession(nyamSession) and showNYAM ? color.new(nyamColor, 95) : na)
bgcolor(inSession(lunchSession) and showLunch ? color.new(lunchColor, 95) : na)
bgcolor(inSession(nypmSession) and showNYPM ? color.new(nypmColor, 95) : na)