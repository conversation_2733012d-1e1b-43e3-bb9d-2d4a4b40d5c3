Script Architecture Analysis
Core Components
Functions:

inSession(sessionStr) - Session presence detector
newSession(sessionStr) - Session boundary detector
getCurrentTradingDay() - Trading day calculator using time_tradingday
cleanOldSessions(...) - Array maintenance/cleanup
storeSession(...) - Array data insertion
drawSessions(...) - Visualization renderer

Data Structures:

Session Arrays (6 sets, 4 arrays each):

asiaHighs/Lows/Bars/Days
londonHighs/Lows/Bars/Days
preMarketHighs/Lows/Bars/Days
nyamHighs/Lows/Bars/Days
lunchHighs/Lows/Bars/Days
nypmHighs/Lows/Bars/Days


Current Session State:

currentSessionHigh/Low/Bar
currentSessionType



Architecture Layers

Configuration Layer

Input controls (toggles, colors, visual settings)
Session time definitions


Data Storage Layer

Array-based multi-day storage
Replaces single-variable approach from original


Session Management Layer

Sequential session tracking
State transitions between sessions
Trading day boundary handling


Data Lifecycle Layer

Array population (storeSession)
Array cleanup (cleanOldSessions)
3-day window enforcement


Visualization Layer

Line drawing for levels
Label generation
Background coloring



Key Differences from Original
Original Design:

Single variables per session type
Only stored most recent session
Overwrote on new session start

Array Design:

Multiple sessions per type stored
Historical preservation
Dynamic window management

Component Flow
Session Detection → Current State Update → Session Storage → Array Cleanup → Visualization
       ↓                    ↓                     ↓               ↓              ↓
  newSession()      currentSessionHigh    storeSession()  cleanOldSessions() drawSessions()
                    currentSessionLow
External Dependencies:

time_tradingday (Pine built-in)
Standard Pine array functions
Pine drawing objects (line, label)

All core logic is custom-built for this specific multi-session tracking requirement.