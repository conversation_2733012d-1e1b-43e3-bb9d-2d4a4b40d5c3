//@version=6
indicator("Enhanced 3-Day Session Liquidity", "ESL", overlay=true, max_lines_count=200, max_labels_count=200, max_boxes_count=50)

// ═══════════════════════════════════════════════════════════════════════════
//                               INPUTS
// ═══════════════════════════════════════════════════════════════════════════

// Session toggles
showAsia = input.bool(true, "Show Asia", group="Sessions")
showLondon = input.bool(true, "Show London", group="Sessions")
showPreMarket = input.bool(true, "Show Pre-Market", group="Sessions")
showNYAM = input.bool(true, "Show NY AM", group="Sessions")
showLunch = input.bool(true, "Show Lunch", group="Sessions")
showNYPM = input.bool(true, "Show NY PM", group="Sessions")

// Level toggles
showHighs = input.bool(true, "Show Session Highs", group="Levels")
showLows = input.bool(true, "Show Session Lows", group="Levels")
showMitigated = input.bool(true, "Show Mitigated Levels", group="Levels")

// Visual settings
daysToShow = input.int(3, "Days to Show", minval=1, maxval=5, group="Visual")
showLabels = input.bool(true, "Show Labels", group="Visual")
showStatusTable = input.bool(true, "Show Status Table", group="Visual")

// Colors
asiaColor = input.color(color.purple, "Asia", group="Colors")
londonColor = input.color(color.blue, "London", group="Colors")
preMarketColor = input.color(color.yellow, "Pre-Market", group="Colors")
nyamColor = input.color(color.green, "NY AM", group="Colors")
lunchColor = input.color(color.gray, "Lunch", group="Colors")
nypmColor = input.color(color.orange, "NY PM", group="Colors")
mitigatedColor = input.color(color.red, "Mitigated", group="Colors")

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION STORAGE ARRAYS
// ═══════════════════════════════════════════════════════════════════════════

// Arrays to store multiple days of each session (6 sessions × 6 arrays each)
var array<float> asiaHighs = array.new<float>()
var array<float> asiaLows = array.new<float>()
var array<int> asiaBars = array.new<int>()
var array<int> asiaDays = array.new<int>()
var array<bool> asiaHighsMitigated = array.new<bool>()
var array<bool> asiaLowsMitigated = array.new<bool>()

var array<float> londonHighs = array.new<float>()
var array<float> londonLows = array.new<float>()
var array<int> londonBars = array.new<int>()
var array<int> londonDays = array.new<int>()
var array<bool> londonHighsMitigated = array.new<bool>()
var array<bool> londonLowsMitigated = array.new<bool>()

var array<float> preMarketHighs = array.new<float>()
var array<float> preMarketLows = array.new<float>()
var array<int> preMarketBars = array.new<int>()
var array<int> preMarketDays = array.new<int>()
var array<bool> preMarketHighsMitigated = array.new<bool>()
var array<bool> preMarketLowsMitigated = array.new<bool>()

var array<float> nyamHighs = array.new<float>()
var array<float> nyamLows = array.new<float>()
var array<int> nyamBars = array.new<int>()
var array<int> nyamDays = array.new<int>()
var array<bool> nyamHighsMitigated = array.new<bool>()
var array<bool> nyamLowsMitigated = array.new<bool>()

var array<float> lunchHighs = array.new<float>()
var array<float> lunchLows = array.new<float>()
var array<int> lunchBars = array.new<int>()
var array<int> lunchDays = array.new<int>()
var array<bool> lunchHighsMitigated = array.new<bool>()
var array<bool> lunchLowsMitigated = array.new<bool>()

var array<float> nypmHighs = array.new<float>()
var array<float> nypmLows = array.new<float>()
var array<int> nypmBars = array.new<int>()
var array<int> nypmDays = array.new<int>()
var array<bool> nypmHighsMitigated = array.new<bool>()
var array<bool> nypmLowsMitigated = array.new<bool>()

// Current session tracking
var float currentSessionHigh = na
var float currentSessionLow = na
var int currentSessionBar = na
var string currentSessionType = ""

// ═══════════════════════════════════════════════════════════════════════════
//                          CORE FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════

inSession(sessionStr) =>
    not na(time(timeframe.period, sessionStr, "America/New_York"))

newSession(sessionStr) =>
    t = time(timeframe.period, sessionStr, "America/New_York")
    na(t[1]) and not na(t)

getCurrentTradingDay() =>
    dayofmonth(time_tradingday, "America/New_York")

// Enhanced cleanup with mitigation arrays
cleanOldSessions(highs, lows, bars, days, highsMitigated, lowsMitigated, maxDays) =>
    if array.size(days) == 0
        return true
    
    currentDay = getCurrentTradingDay()
    while array.size(days) > 0
        oldestDay = array.get(days, array.size(days) - 1)
        dayDiff = currentDay - oldestDay
        if dayDiff < -20
            dayDiff := dayDiff + 31
        else if dayDiff > 20
            dayDiff := dayDiff - 31
        
        if dayDiff > maxDays
            array.pop(highs)
            array.pop(lows)
            array.pop(bars)
            array.pop(days)
            array.pop(highsMitigated)
            array.pop(lowsMitigated)
        else
            break
    true

// Enhanced session storage with mitigation tracking
storeSession(highs, lows, bars, days, highsMitigated, lowsMitigated, sessionHigh, sessionLow, sessionBar, sessionDay) =>
    array.unshift(highs, sessionHigh)
    array.unshift(lows, sessionLow)
    array.unshift(bars, sessionBar)
    array.unshift(days, sessionDay)
    array.unshift(highsMitigated, false)
    array.unshift(lowsMitigated, false)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DEFINITIONS
// ═══════════════════════════════════════════════════════════════════════════

asiaSession = "1900-0000:23456"
londonSession = "0200-0500:23456"
preMarketSession = "0700-0930:23456"
nyamSession = "0930-1200:23456"
lunchSession = "1200-1300:23456"
nypmSession = "1330-1610:23456"

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DETECTION & TRACKING
// ═══════════════════════════════════════════════════════════════════════════

// Track current session with confirmed bar logic to prevent repainting
if newSession(asiaSession)
    if currentSessionType != "" and barstate.isconfirmed[1]
        // Store previous session
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        if currentSessionType == "Asia"
            storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "London"
            storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "PreMarket"
            storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYAM"
            storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "Lunch"
            storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYPM"
            storeSession(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "Asia"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(londonSession)
    if currentSessionType == "Asia" and barstate.isconfirmed[1]
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "London"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(preMarketSession)
    if currentSessionType == "London" and barstate.isconfirmed[1]
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "PreMarket"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(nyamSession)
    if currentSessionType == "PreMarket" and barstate.isconfirmed[1]
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "NYAM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(lunchSession)
    if currentSessionType == "NYAM" and barstate.isconfirmed[1]
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "Lunch"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if newSession(nypmSession)
    if currentSessionType == "Lunch" and barstate.isconfirmed[1]
        tradingDay = dayofmonth(time_tradingday[1], "America/New_York")
        storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "NYPM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

// Update current session highs/lows only on confirmed bars to prevent repainting
if currentSessionType != "" and barstate.isconfirmed
    if high > currentSessionHigh
        currentSessionHigh := high
        currentSessionBar := bar_index
    if low < currentSessionLow
        currentSessionLow := low

// Store final session at end of data
if barstate.islast and currentSessionType != ""
    tradingDay = dayofmonth(time_tradingday, "America/New_York")
    if currentSessionType == "Asia"
        storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "London"
        storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "PreMarket"
        storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "NYAM"
        storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "Lunch"
        storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "NYPM"
        storeSession(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

// Clean old sessions
if barstate.isconfirmed
    cleanOldSessions(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, daysToShow)
    cleanOldSessions(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, daysToShow)
    cleanOldSessions(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, daysToShow)
    cleanOldSessions(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, daysToShow)
    cleanOldSessions(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, daysToShow)
    cleanOldSessions(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, daysToShow)

// ═══════════════════════════════════════════════════════════════════════════
//                          MITIGATION DETECTION
// ═══════════════════════════════════════════════════════════════════════════

// Update mitigation status for all stored sessions
updateMitigation(highs, lows, highsMitigated, lowsMitigated) =>
    if array.size(highs) > 0
        for i = 0 to array.size(highs) - 1
            // Check high mitigation
            if not array.get(highsMitigated, i) and high > array.get(highs, i)
                array.set(highsMitigated, i, true)

            // Check low mitigation
            if not array.get(lowsMitigated, i) and low < array.get(lows, i)
                array.set(lowsMitigated, i, true)

// Update mitigation for all sessions
if barstate.isconfirmed
    updateMitigation(asiaHighs, asiaLows, asiaHighsMitigated, asiaLowsMitigated)
    updateMitigation(londonHighs, londonLows, londonHighsMitigated, londonLowsMitigated)
    updateMitigation(preMarketHighs, preMarketLows, preMarketHighsMitigated, preMarketLowsMitigated)
    updateMitigation(nyamHighs, nyamLows, nyamHighsMitigated, nyamLowsMitigated)
    updateMitigation(lunchHighs, lunchLows, lunchHighsMitigated, lunchLowsMitigated)
    updateMitigation(nypmHighs, nypmLows, nypmHighsMitigated, nypmLowsMitigated)

// ═══════════════════════════════════════════════════════════════════════════
//                          ENHANCED VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════

// Enhanced level drawing function with mitigation support
drawLevel(levelPrice, levelBar, levelColor, sessionName, isHigh, isMitigated, showSession, showType, tradingDay) =>
    if showSession and showType and not na(levelPrice)
        // Determine line style and color based on mitigation
        actualColor = isMitigated and showMitigated ? mitigatedColor : levelColor
        actualStyle = isMitigated ? line.style_dashed : line.style_solid
        actualWidth = isMitigated ? 1 : 2

        // Only draw if conditions are met
        shouldDraw = isMitigated ? showMitigated : true

        if shouldDraw
            // Draw line extending to the right
            line.new(levelBar, levelPrice, bar_index, levelPrice, color=actualColor, width=actualWidth, style=actualStyle, extend=extend.right)

            // Draw label if enabled
            if showLabels
                labelText = sessionName + (isHigh ? " H" : " L")
                if isMitigated
                    labelText := labelText + " ✓"

                // Add price and trading day info
                labelText := labelText + "\n" + str.tostring(levelPrice, format.mintick)
                labelText := labelText + "\nTD:" + str.tostring(tradingDay)

                labelStyle = isHigh ? label.style_label_down : label.style_label_up
                label.new(levelBar, levelPrice, labelText, style=labelStyle, color=actualColor, textcolor=color.white, size=size.small)

// Enhanced session drawing function
drawSessionsEnhanced(highs, lows, bars, days, highsMitigated, lowsMitigated, sessionColor, sessionName, showSession) =>
    if showSession and array.size(highs) > 0
        for i = 0 to math.min(array.size(highs) - 1, daysToShow - 1)
            if i < array.size(highs) and i < array.size(lows) and i < array.size(bars) and i < array.size(days)
                sessionHigh = array.get(highs, i)
                sessionLow = array.get(lows, i)
                sessionBar = array.get(bars, i)
                sessionDay = array.get(days, i)
                highMitigated = array.get(highsMitigated, i)
                lowMitigated = array.get(lowsMitigated, i)

                // Draw high and low levels
                drawLevel(sessionHigh, sessionBar, sessionColor, sessionName, true, highMitigated, true, showHighs, sessionDay)
                drawLevel(sessionLow, sessionBar, sessionColor, sessionName, false, lowMitigated, true, showLows, sessionDay)

// ═══════════════════════════════════════════════════════════════════════════
//                          DRAW ALL SESSIONS
// ═══════════════════════════════════════════════════════════════════════════

// Draw all sessions with enhanced visualization
drawSessionsEnhanced(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, asiaColor, "Asia", showAsia)
drawSessionsEnhanced(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, londonColor, "London", showLondon)
drawSessionsEnhanced(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, preMarketColor, "PreMkt", showPreMarket)
drawSessionsEnhanced(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, nyamColor, "NYAM", showNYAM)
drawSessionsEnhanced(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, lunchColor, "Lunch", showLunch)
drawSessionsEnhanced(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, nypmColor, "NYPM", showNYPM)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION BACKGROUNDS
// ═══════════════════════════════════════════════════════════════════════════

// Session backgrounds
bgcolor(inSession(asiaSession) and showAsia ? color.new(asiaColor, 95) : na, title="Asia Session")
bgcolor(inSession(londonSession) and showLondon ? color.new(londonColor, 95) : na, title="London Session")
bgcolor(inSession(preMarketSession) and showPreMarket ? color.new(preMarketColor, 95) : na, title="Pre-Market Session")
bgcolor(inSession(nyamSession) and showNYAM ? color.new(nyamColor, 95) : na, title="NY AM Session")
bgcolor(inSession(lunchSession) and showLunch ? color.new(lunchColor, 95) : na, title="Lunch Session")
bgcolor(inSession(nypmSession) and showNYPM ? color.new(nypmColor, 95) : na, title="NY PM Session")

// ═══════════════════════════════════════════════════════════════════════════
//                          STATUS TABLE
// ═══════════════════════════════════════════════════════════════════════════

// Status table for debugging and monitoring
var table statusTable = table.new(position.top_right, 4, 8, bgcolor=color.new(color.black, 80))

if showStatusTable and barstate.islast
    currentTD = getCurrentTradingDay()
    currentNYHour = hour(timenow, "America/New_York")

    // Header
    table.cell(statusTable, 0, 0, "Session", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 1, 0, "High", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 2, 0, "Low", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 3, 0, "Count", text_color=color.white, bgcolor=color.new(color.gray, 50))

    // Current Trading Day Info
    table.cell(statusTable, 0, 1, "Current TD", text_color=color.white, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 1, 1, str.tostring(currentTD), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 2, 1, "HR:" + str.tostring(currentNYHour), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 3, 1, "Days:" + str.tostring(daysToShow), text_color=color.yellow, bgcolor=color.new(color.blue, 50))

    // Session status rows
    getMitigationStatus(highsMitigated, lowsMitigated, index) =>
        if array.size(highsMitigated) > index
            highMit = array.get(highsMitigated, index) ? "✓" : "○"
            lowMit = array.get(lowsMitigated, index) ? "✓" : "○"
            [highMit, lowMit]
        else
            ["", ""]

    // Asia
    [asiaHighStatus, asiaLowStatus] = getMitigationStatus(asiaHighsMitigated, asiaLowsMitigated, 0)
    table.cell(statusTable, 0, 2, "Asia", text_color=color.white)
    table.cell(statusTable, 1, 2, asiaHighStatus, text_color=asiaColor)
    table.cell(statusTable, 2, 2, asiaLowStatus, text_color=asiaColor)
    table.cell(statusTable, 3, 2, str.tostring(array.size(asiaHighs)), text_color=color.white)

    // London
    [londonHighStatus, londonLowStatus] = getMitigationStatus(londonHighsMitigated, londonLowsMitigated, 0)
    table.cell(statusTable, 0, 3, "London", text_color=color.white)
    table.cell(statusTable, 1, 3, londonHighStatus, text_color=londonColor)
    table.cell(statusTable, 2, 3, londonLowStatus, text_color=londonColor)
    table.cell(statusTable, 3, 3, str.tostring(array.size(londonHighs)), text_color=color.white)

    // Pre-Market
    [preMarketHighStatus, preMarketLowStatus] = getMitigationStatus(preMarketHighsMitigated, preMarketLowsMitigated, 0)
    table.cell(statusTable, 0, 4, "PreMkt", text_color=color.white)
    table.cell(statusTable, 1, 4, preMarketHighStatus, text_color=preMarketColor)
    table.cell(statusTable, 2, 4, preMarketLowStatus, text_color=preMarketColor)
    table.cell(statusTable, 3, 4, str.tostring(array.size(preMarketHighs)), text_color=color.white)

    // NY AM
    [nyamHighStatus, nyamLowStatus] = getMitigationStatus(nyamHighsMitigated, nyamLowsMitigated, 0)
    table.cell(statusTable, 0, 5, "NY AM", text_color=color.white)
    table.cell(statusTable, 1, 5, nyamHighStatus, text_color=nyamColor)
    table.cell(statusTable, 2, 5, nyamLowStatus, text_color=nyamColor)
    table.cell(statusTable, 3, 5, str.tostring(array.size(nyamHighs)), text_color=color.white)

    // Lunch
    [lunchHighStatus, lunchLowStatus] = getMitigationStatus(lunchHighsMitigated, lunchLowsMitigated, 0)
    table.cell(statusTable, 0, 6, "Lunch", text_color=color.white)
    table.cell(statusTable, 1, 6, lunchHighStatus, text_color=lunchColor)
    table.cell(statusTable, 2, 6, lunchLowStatus, text_color=lunchColor)
    table.cell(statusTable, 3, 6, str.tostring(array.size(lunchHighs)), text_color=color.white)

    // NY PM
    [nypmHighStatus, nypmLowStatus] = getMitigationStatus(nypmHighsMitigated, nypmLowsMitigated, 0)
    table.cell(statusTable, 0, 7, "NY PM", text_color=color.white)
    table.cell(statusTable, 1, 7, nypmHighStatus, text_color=nypmColor)
    table.cell(statusTable, 2, 7, nypmLowStatus, text_color=nypmColor)
    table.cell(statusTable, 3, 7, str.tostring(array.size(nypmHighs)), text_color=color.white)
