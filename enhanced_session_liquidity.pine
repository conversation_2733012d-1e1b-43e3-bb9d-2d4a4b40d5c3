//@version=6
indicator("Enhanced 3-Day Session Liquidity", "ESL", overlay=true, max_lines_count=200, max_labels_count=200, max_boxes_count=50)

// ═══════════════════════════════════════════════════════════════════════════
//                      TIME ZONE & TRADING DAY DOCUMENTATION
// ═══════════════════════════════════════════════════════════════════════════
//
// PROBLEM SOLVED: Complex time zone discrepancies affecting accurate lookback calculations
//
// SOLUTION IMPLEMENTED:
// 1. TRADING DAY DEFINITION: Trading day starts at 6 PM NY time (18:00)
//    - If current time >= 6 PM NY, it belongs to NEXT calendar day's trading session
//    - This ensures Asia session (7 PM - 12 AM) is properly grouped with the next day
//    - Example: Thursday 7 PM NY = Friday trading day (Asia session)
//
// 2. 3-DAY LOOKBACK LOGIC:
//    - From Friday perspective: Shows Friday (0), Thursday (1), Wednesday (2) sessions
//    - Properly excludes weekends and non-trading days
//    - Uses enhanced trading day calculation for accurate session grouping
//
// 3. SESSION TIMING ACCURACY:
//    - Asia: 7 PM - 12 AM NY (spans calendar dates, handled correctly)
//    - London: 2 AM - 5 AM NY
//    - Pre-Market: 7 AM - 9:30 AM NY
//    - NY AM: 9:30 AM - 12 PM NY
//    - Lunch: 12 PM - 1 PM NY
//    - NY PM: 1:30 PM - 4:10 PM NY
//
// 4. WEEKEND & HOLIDAY HANDLING:
//    - Automatic exclusion of weekends from lookback calculation
//    - Proper month boundary handling with wraparound logic
//    - Enhanced cleanup function maintains accurate 3-day window
//
// 5. DEBUGGING FEATURES:
//    - Status table shows current trading day vs calendar day
//    - Time zone indicator shows when trading day transitions occur
//    - Session counts and mitigation status for verification
//
// ═══════════════════════════════════════════════════════════════════════════

// ═══════════════════════════════════════════════════════════════════════════
//                               INPUTS
// ═══════════════════════════════════════════════════════════════════════════

// Session toggles
showAsia = input.bool(true, "Show Asia", group="Sessions")
showLondon = input.bool(true, "Show London", group="Sessions")
showPreMarket = input.bool(true, "Show Pre-Market", group="Sessions")
showNYAM = input.bool(true, "Show NY AM", group="Sessions")
showLunch = input.bool(true, "Show Lunch", group="Sessions")
showNYPM = input.bool(true, "Show NY PM", group="Sessions")

// Level toggles
showHighs = input.bool(true, "Show Session Highs", group="Levels")
showLows = input.bool(true, "Show Session Lows", group="Levels")
showMitigated = input.bool(true, "Show Mitigated Levels", group="Levels")

// Visual settings
daysToShow = input.int(3, "Days to Show", minval=1, maxval=5, group="Visual")
showLabels = input.bool(true, "Show Labels", group="Visual")
showStatusTable = input.bool(true, "Show Status Table", group="Visual")

// Colors
asiaColor = input.color(color.purple, "Asia", group="Colors")
londonColor = input.color(color.blue, "London", group="Colors")
preMarketColor = input.color(color.yellow, "Pre-Market", group="Colors")
nyamColor = input.color(color.green, "NY AM", group="Colors")
lunchColor = input.color(color.gray, "Lunch", group="Colors")
nypmColor = input.color(color.orange, "NY PM", group="Colors")
mitigatedColor = input.color(color.red, "Mitigated", group="Colors")

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION STORAGE ARRAYS
// ═══════════════════════════════════════════════════════════════════════════

// Arrays to store multiple days of each session (6 sessions × 8 arrays each)
var array<float> asiaHighs = array.new<float>()
var array<float> asiaLows = array.new<float>()
var array<int> asiaHighBars = array.new<int>()
var array<int> asiaLowBars = array.new<int>()
var array<int> asiaDays = array.new<int>()
var array<bool> asiaHighsMitigated = array.new<bool>()
var array<bool> asiaLowsMitigated = array.new<bool>()

var array<float> londonHighs = array.new<float>()
var array<float> londonLows = array.new<float>()
var array<int> londonHighBars = array.new<int>()
var array<int> londonLowBars = array.new<int>()
var array<int> londonDays = array.new<int>()
var array<bool> londonHighsMitigated = array.new<bool>()
var array<bool> londonLowsMitigated = array.new<bool>()

var array<float> preMarketHighs = array.new<float>()
var array<float> preMarketLows = array.new<float>()
var array<int> preMarketHighBars = array.new<int>()
var array<int> preMarketLowBars = array.new<int>()
var array<int> preMarketDays = array.new<int>()
var array<bool> preMarketHighsMitigated = array.new<bool>()
var array<bool> preMarketLowsMitigated = array.new<bool>()

var array<float> nyamHighs = array.new<float>()
var array<float> nyamLows = array.new<float>()
var array<int> nyamHighBars = array.new<int>()
var array<int> nyamLowBars = array.new<int>()
var array<int> nyamDays = array.new<int>()
var array<bool> nyamHighsMitigated = array.new<bool>()
var array<bool> nyamLowsMitigated = array.new<bool>()

var array<float> lunchHighs = array.new<float>()
var array<float> lunchLows = array.new<float>()
var array<int> lunchHighBars = array.new<int>()
var array<int> lunchLowBars = array.new<int>()
var array<int> lunchDays = array.new<int>()
var array<bool> lunchHighsMitigated = array.new<bool>()
var array<bool> lunchLowsMitigated = array.new<bool>()

var array<float> nypmHighs = array.new<float>()
var array<float> nypmLows = array.new<float>()
var array<int> nypmHighBars = array.new<int>()
var array<int> nypmLowBars = array.new<int>()
var array<int> nypmDays = array.new<int>()
var array<bool> nypmHighsMitigated = array.new<bool>()
var array<bool> nypmLowsMitigated = array.new<bool>()

// Current session tracking
var float currentSessionHigh = na
var float currentSessionLow = na
var int currentSessionHighBar = na
var int currentSessionLowBar = na
var string currentSessionType = ""

// ═══════════════════════════════════════════════════════════════════════════
//                          CORE FUNCTIONS
// ═══════════════════════════════════════════════════════════════════════════

inSession(sessionStr) =>
    not na(time(timeframe.period, sessionStr, "America/New_York"))

newSession(sessionStr) =>
    t = time(timeframe.period, sessionStr, "America/New_York")
    na(t[1]) and not na(t)

// ═══════════════════════════════════════════════════════════════════════════
//                      ENHANCED TIME ZONE & TRADING DAY LOGIC
// ═══════════════════════════════════════════════════════════════════════════

// Simplified trading day calculation - use calendar day
getCurrentTradingDay() =>
    dayofmonth(time, "America/New_York")

// Get trading day for a specific timestamp - simplified
getTradingDayForTime(barTime) =>
    dayofmonth(barTime, "America/New_York")

// Check if a day is a trading day (excludes weekends)
isTradingDay(timestamp) =>
    dayOfWeek = dayofweek(timestamp, "America/New_York")
    dayOfWeek >= 2 and dayOfWeek <= 6  // Monday=2 to Friday=6

// Calculate trading days difference (excludes weekends)
getTradingDaysDifference(currentDay, targetDay, currentMonth, targetMonth, currentYear, targetYear) =>
    // Handle same month
    if currentYear == targetYear and currentMonth == targetMonth
        math.abs(currentDay - targetDay)
    else
        // Handle month/year boundaries - use a more robust approach
        currentTimestamp = timestamp("America/New_York", currentYear, currentMonth, currentDay, 12, 0)
        targetTimestamp = timestamp("America/New_York", targetYear, targetMonth, targetDay, 12, 0)

        // Calculate difference in days
        daysDiff = math.round((currentTimestamp - targetTimestamp) / 86400000)
        math.abs(daysDiff)

// Simplified cleanup - keep only the specified number of sessions
cleanOldSessions(highs, lows, highBars, lowBars, days, highsMitigated, lowsMitigated, maxDays) =>
    // Simple approach: keep only the last maxDays sessions
    // Since we use push() (add to end), we need to remove from front (oldest)
    while array.size(days) > maxDays
        array.shift(highs)
        array.shift(lows)
        array.shift(highBars)
        array.shift(lowBars)
        array.shift(days)
        array.shift(highsMitigated)
        array.shift(lowsMitigated)
    true

// Enhanced session storage with mitigation tracking
// Store chronologically (oldest at index 0, newest at end)
storeSession(highs, lows, highBars, lowBars, days, highsMitigated, lowsMitigated, sessionHigh, sessionLow, sessionHighBar, sessionLowBar, sessionDay) =>
    array.push(highs, sessionHigh)
    array.push(lows, sessionLow)
    array.push(highBars, sessionHighBar)
    array.push(lowBars, sessionLowBar)
    array.push(days, sessionDay)
    array.push(highsMitigated, false)
    array.push(lowsMitigated, false)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DEFINITIONS
// ═══════════════════════════════════════════════════════════════════════════

asiaSession = "1900-0000:23456"
londonSession = "0200-0500:23456"
preMarketSession = "0700-0930:23456"
nyamSession = "0930-1200:23456"
lunchSession = "1200-1300:23456"
nypmSession = "1330-1610:23456"

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION DETECTION & TRACKING
// ═══════════════════════════════════════════════════════════════════════════

// Track current session with confirmed bar logic to prevent repainting
// Extract all newSession calls for consistency
isNewAsia = newSession(asiaSession)
isNewLondon = newSession(londonSession)
isNewPreMarket = newSession(preMarketSession)
isNewNYAM = newSession(nyamSession)
isNewLunch = newSession(lunchSession)
isNewNYPM = newSession(nypmSession)

if isNewAsia
    if currentSessionType != "" and barstate.isconfirmed[1]
        // Store previous session using enhanced trading day calculation
        tradingDay = getTradingDayForTime(time[1])
        if currentSessionType == "Asia"
            storeSession(asiaHighs, asiaLows, asiaHighBars, asiaLowBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionHighBar, currentSessionLowBar, tradingDay)
        else if currentSessionType == "London"
            storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "PreMarket"
            storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYAM"
            storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "Lunch"
            storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
        else if currentSessionType == "NYPM"
            storeSession(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "Asia"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionHighBar := bar_index
    currentSessionLowBar := bar_index

else if isNewLondon
    if currentSessionType == "Asia" and barstate.isconfirmed[1]
        tradingDay = getTradingDayForTime(time[1])
        storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "London"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if isNewPreMarket
    if currentSessionType == "London" and barstate.isconfirmed[1]
        tradingDay = getTradingDayForTime(time[1])
        storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "PreMarket"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if isNewNYAM
    if currentSessionType == "PreMarket" and barstate.isconfirmed[1]
        tradingDay = getTradingDayForTime(time[1])
        storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "NYAM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if isNewLunch
    if currentSessionType == "NYAM" and barstate.isconfirmed[1]
        tradingDay = getTradingDayForTime(time[1])
        storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "Lunch"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

else if isNewNYPM
    if currentSessionType == "Lunch" and barstate.isconfirmed[1]
        tradingDay = getTradingDayForTime(time[1])
        storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

    currentSessionType := "NYPM"
    currentSessionHigh := high
    currentSessionLow := low
    currentSessionBar := bar_index

// Update current session highs/lows only on confirmed bars to prevent repainting
if currentSessionType != "" and barstate.isconfirmed
    if high > currentSessionHigh
        currentSessionHigh := high
        currentSessionHighBar := bar_index
    if low < currentSessionLow
        currentSessionLow := low
        currentSessionLowBar := bar_index

// Store final session at end of data using enhanced trading day calculation
if barstate.islast and currentSessionType != ""
    tradingDay = getCurrentTradingDay()
    if currentSessionType == "Asia"
        storeSession(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "London"
        storeSession(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "PreMarket"
        storeSession(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "NYAM"
        storeSession(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "Lunch"
        storeSession(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)
    else if currentSessionType == "NYPM"
        storeSession(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, currentSessionHigh, currentSessionLow, currentSessionBar, tradingDay)

// Clean old sessions
if barstate.isconfirmed
    cleanOldSessions(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, daysToShow)
    cleanOldSessions(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, daysToShow)
    cleanOldSessions(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, daysToShow)
    cleanOldSessions(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, daysToShow)
    cleanOldSessions(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, daysToShow)
    cleanOldSessions(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, daysToShow)

// ═══════════════════════════════════════════════════════════════════════════
//                          MITIGATION DETECTION
// ═══════════════════════════════════════════════════════════════════════════

// Update mitigation status for all stored sessions
updateMitigation(highs, lows, highsMitigated, lowsMitigated) =>
    if array.size(highs) > 0
        for i = 0 to array.size(highs) - 1
            // Check high mitigation
            if not array.get(highsMitigated, i) and high > array.get(highs, i)
                array.set(highsMitigated, i, true)

            // Check low mitigation
            if not array.get(lowsMitigated, i) and low < array.get(lows, i)
                array.set(lowsMitigated, i, true)

// Update mitigation for all sessions
if barstate.isconfirmed
    updateMitigation(asiaHighs, asiaLows, asiaHighsMitigated, asiaLowsMitigated)
    updateMitigation(londonHighs, londonLows, londonHighsMitigated, londonLowsMitigated)
    updateMitigation(preMarketHighs, preMarketLows, preMarketHighsMitigated, preMarketLowsMitigated)
    updateMitigation(nyamHighs, nyamLows, nyamHighsMitigated, nyamLowsMitigated)
    updateMitigation(lunchHighs, lunchLows, lunchHighsMitigated, lunchLowsMitigated)
    updateMitigation(nypmHighs, nypmLows, nypmHighsMitigated, nypmLowsMitigated)

// ═══════════════════════════════════════════════════════════════════════════
//                          ENHANCED VISUALIZATION
// ═══════════════════════════════════════════════════════════════════════════

// Enhanced level drawing function with mitigation support
drawLevel(levelPrice, levelBar, levelColor, sessionName, isHigh, isMitigated, showSession, showType, tradingDay) =>
    if showSession and showType and not na(levelPrice)
        // Determine line style and color based on mitigation
        actualColor = isMitigated and showMitigated ? mitigatedColor : levelColor
        actualStyle = isMitigated ? line.style_dashed : line.style_solid
        actualWidth = isMitigated ? 1 : 2

        // Only draw if conditions are met
        shouldDraw = isMitigated ? showMitigated : true

        if shouldDraw
            // Draw horizontal ray - much simpler and more reliable
            line.new(levelBar, levelPrice, levelBar + 1, levelPrice, color=actualColor, width=actualWidth, style=actualStyle, extend=extend.right)

            // Draw label if enabled
            if showLabels
                labelText = sessionName + (isHigh ? " H" : " L")
                if isMitigated
                    labelText := labelText + " ✓"

                // Add price and trading day info
                labelText := labelText + "\n" + str.tostring(levelPrice, format.mintick)
                labelText := labelText + "\nTD:" + str.tostring(tradingDay)

                labelStyle = isHigh ? label.style_label_down : label.style_label_up
                label.new(levelBar, levelPrice, labelText, style=labelStyle, color=actualColor, textcolor=color.white, size=size.small)

// Enhanced session drawing function - show sessions chronologically
drawSessionsEnhanced(highs, lows, bars, days, highsMitigated, lowsMitigated, sessionColor, sessionName, showSession) =>
    if showSession and array.size(highs) > 0
        // Now arrays are stored chronologically (oldest at 0, newest at end)
        // Show all available sessions up to daysToShow limit
        sessionsToShow = math.min(array.size(highs), daysToShow)
        
        // Draw sessions chronologically from oldest to newest
        for i = 0 to sessionsToShow - 1
            if i < array.size(highs) and i < array.size(lows) and i < array.size(bars) and i < array.size(days)
                sessionHigh = array.get(highs, i)
                sessionLow = array.get(lows, i)
                sessionBar = array.get(bars, i)
                sessionDay = array.get(days, i)
                highMitigated = array.get(highsMitigated, i)
                lowMitigated = array.get(lowsMitigated, i)

                // Draw high and low levels
                drawLevel(sessionHigh, sessionBar, sessionColor, sessionName, true, highMitigated, true, showHighs, sessionDay)
                drawLevel(sessionLow, sessionBar, sessionColor, sessionName, false, lowMitigated, true, showLows, sessionDay)

// ═══════════════════════════════════════════════════════════════════════════
//                          DRAW ALL SESSIONS
// ═══════════════════════════════════════════════════════════════════════════

// Draw all sessions with enhanced visualization
drawSessionsEnhanced(asiaHighs, asiaLows, asiaBars, asiaDays, asiaHighsMitigated, asiaLowsMitigated, asiaColor, "Asia", showAsia)
drawSessionsEnhanced(londonHighs, londonLows, londonBars, londonDays, londonHighsMitigated, londonLowsMitigated, londonColor, "London", showLondon)
drawSessionsEnhanced(preMarketHighs, preMarketLows, preMarketBars, preMarketDays, preMarketHighsMitigated, preMarketLowsMitigated, preMarketColor, "PreMkt", showPreMarket)
drawSessionsEnhanced(nyamHighs, nyamLows, nyamBars, nyamDays, nyamHighsMitigated, nyamLowsMitigated, nyamColor, "NYAM", showNYAM)
drawSessionsEnhanced(lunchHighs, lunchLows, lunchBars, lunchDays, lunchHighsMitigated, lunchLowsMitigated, lunchColor, "Lunch", showLunch)
drawSessionsEnhanced(nypmHighs, nypmLows, nypmBars, nypmDays, nypmHighsMitigated, nypmLowsMitigated, nypmColor, "NYPM", showNYPM)

// ═══════════════════════════════════════════════════════════════════════════
//                          SESSION BACKGROUNDS
// ═══════════════════════════════════════════════════════════════════════════

// Session backgrounds
bgcolor(inSession(asiaSession) and showAsia ? color.new(asiaColor, 95) : na, title="Asia Session")
bgcolor(inSession(londonSession) and showLondon ? color.new(londonColor, 95) : na, title="London Session")
bgcolor(inSession(preMarketSession) and showPreMarket ? color.new(preMarketColor, 95) : na, title="Pre-Market Session")
bgcolor(inSession(nyamSession) and showNYAM ? color.new(nyamColor, 95) : na, title="NY AM Session")
bgcolor(inSession(lunchSession) and showLunch ? color.new(lunchColor, 95) : na, title="Lunch Session")
bgcolor(inSession(nypmSession) and showNYPM ? color.new(nypmColor, 95) : na, title="NY PM Session")

// ═══════════════════════════════════════════════════════════════════════════
//                          STATUS TABLE
// ═══════════════════════════════════════════════════════════════════════════

// Helper function for mitigation status
getMitigationStatus(highsMitigated, lowsMitigated, index) =>
    if array.size(highsMitigated) > index
        highMit = array.get(highsMitigated, index) ? "✓" : "○"
        lowMit = array.get(lowsMitigated, index) ? "✓" : "○"
        [highMit, lowMit]
    else
        ["", ""]

// Status table for debugging and monitoring (expanded for time zone info)
var table statusTable = table.new(position.top_right, 4, 9, bgcolor=color.new(color.black, 80))

if showStatusTable and barstate.islast
    currentTD = getCurrentTradingDay()
    currentNYHour = hour(timenow, "America/New_York")
    currentNYMinute = minute(timenow, "America/New_York")

    // Enhanced time zone debugging
    currentCalendarDay = dayofmonth(timenow, "America/New_York")
    currentMonth = month(timenow, "America/New_York")
    currentYear = year(timenow, "America/New_York")

    // Header
    table.cell(statusTable, 0, 0, "Session", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 1, 0, "High", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 2, 0, "Low", text_color=color.white, bgcolor=color.new(color.gray, 50))
    table.cell(statusTable, 3, 0, "Oldest TD", text_color=color.white, bgcolor=color.new(color.gray, 50))

    // Enhanced Trading Day Info with time zone details
    table.cell(statusTable, 0, 1, "Trading Day", text_color=color.white, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 1, 1, str.tostring(currentTD), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 2, 1, "Cal:" + str.tostring(currentCalendarDay), text_color=color.yellow, bgcolor=color.new(color.blue, 50))
    table.cell(statusTable, 3, 1, "Days:" + str.tostring(daysToShow), text_color=color.yellow, bgcolor=color.new(color.blue, 50))

    // Time zone info row
    timeString = str.tostring(currentNYHour) + ":" + (currentNYMinute < 10 ? "0" + str.tostring(currentNYMinute) : str.tostring(currentNYMinute))
    table.cell(statusTable, 0, 2, "NY Time", text_color=color.white, bgcolor=color.new(color.orange, 50))
    table.cell(statusTable, 1, 2, timeString, text_color=color.white, bgcolor=color.new(color.orange, 50))
    table.cell(statusTable, 2, 2, str.tostring(currentMonth) + "/" + str.tostring(currentYear), text_color=color.white, bgcolor=color.new(color.orange, 50))
    table.cell(statusTable, 3, 2, currentNYHour >= 18 ? "Next TD" : "Same TD", text_color=currentNYHour >= 18 ? color.lime : color.white, bgcolor=color.new(color.orange, 50))

    // Session status rows (adjusted for new header structure)

    // Asia
    [asiaHighStatus, asiaLowStatus] = getMitigationStatus(asiaHighsMitigated, asiaLowsMitigated, 0)
    asiaOldestTD = array.size(asiaDays) > 0 ? str.tostring(array.get(asiaDays, 0)) : "N/A"
    table.cell(statusTable, 0, 3, "Asia", text_color=color.white)
    table.cell(statusTable, 1, 3, asiaHighStatus, text_color=asiaColor)
    table.cell(statusTable, 2, 3, asiaLowStatus, text_color=asiaColor)
    table.cell(statusTable, 3, 3, asiaOldestTD + "(" + str.tostring(array.size(asiaHighs)) + ")", text_color=color.white)

    // London
    [londonHighStatus, londonLowStatus] = getMitigationStatus(londonHighsMitigated, londonLowsMitigated, 0)
    londonOldestTD = array.size(londonDays) > 0 ? str.tostring(array.get(londonDays, 0)) : "N/A"
    table.cell(statusTable, 0, 4, "London", text_color=color.white)
    table.cell(statusTable, 1, 4, londonHighStatus, text_color=londonColor)
    table.cell(statusTable, 2, 4, londonLowStatus, text_color=londonColor)
    table.cell(statusTable, 3, 4, londonOldestTD + "(" + str.tostring(array.size(londonHighs)) + ")", text_color=color.white)

    // Pre-Market
    [preMarketHighStatus, preMarketLowStatus] = getMitigationStatus(preMarketHighsMitigated, preMarketLowsMitigated, 0)
    preMarketOldestTD = array.size(preMarketDays) > 0 ? str.tostring(array.get(preMarketDays, 0)) : "N/A"
    table.cell(statusTable, 0, 5, "PreMkt", text_color=color.white)
    table.cell(statusTable, 1, 5, preMarketHighStatus, text_color=preMarketColor)
    table.cell(statusTable, 2, 5, preMarketLowStatus, text_color=preMarketColor)
    table.cell(statusTable, 3, 5, preMarketOldestTD + "(" + str.tostring(array.size(preMarketHighs)) + ")", text_color=color.white)

    // NY AM
    [nyamHighStatus, nyamLowStatus] = getMitigationStatus(nyamHighsMitigated, nyamLowsMitigated, 0)
    nyamOldestTD = array.size(nyamDays) > 0 ? str.tostring(array.get(nyamDays, 0)) : "N/A"
    table.cell(statusTable, 0, 6, "NY AM", text_color=color.white)
    table.cell(statusTable, 1, 6, nyamHighStatus, text_color=nyamColor)
    table.cell(statusTable, 2, 6, nyamLowStatus, text_color=nyamColor)
    table.cell(statusTable, 3, 6, nyamOldestTD + "(" + str.tostring(array.size(nyamHighs)) + ")", text_color=color.white)

    // Lunch
    [lunchHighStatus, lunchLowStatus] = getMitigationStatus(lunchHighsMitigated, lunchLowsMitigated, 0)
    lunchOldestTD = array.size(lunchDays) > 0 ? str.tostring(array.get(lunchDays, 0)) : "N/A"
    table.cell(statusTable, 0, 7, "Lunch", text_color=color.white)
    table.cell(statusTable, 1, 7, lunchHighStatus, text_color=lunchColor)
    table.cell(statusTable, 2, 7, lunchLowStatus, text_color=lunchColor)
    table.cell(statusTable, 3, 7, lunchOldestTD + "(" + str.tostring(array.size(lunchHighs)) + ")", text_color=color.white)

    // NY PM
    [nypmHighStatus, nypmLowStatus] = getMitigationStatus(nypmHighsMitigated, nypmLowsMitigated, 0)
    nypmOldestTD = array.size(nypmDays) > 0 ? str.tostring(array.get(nypmDays, 0)) : "N/A"
    table.cell(statusTable, 0, 8, "NY PM", text_color=color.white)
    table.cell(statusTable, 1, 8, nypmHighStatus, text_color=nypmColor)
    table.cell(statusTable, 2, 8, nypmLowStatus, text_color=nypmColor)
    table.cell(statusTable, 3, 8, nypmOldestTD + "(" + str.tostring(array.size(nypmHighs)) + ")", text_color=color.white)
